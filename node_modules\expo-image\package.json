{"name": "expo-image", "title": "Expo Image", "version": "2.2.0", "description": "A cross-platform, performant image component for React Native and Expo with Web support", "main": "src/index.ts", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "homepage": "https://docs.expo.dev/versions/latest/sdk/image/", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-image"}, "keywords": ["react-native"], "author": "650 Industries, Inc.", "license": "MIT", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "7638c800b57fe78f57cc7f129022f58e84a523c5"}
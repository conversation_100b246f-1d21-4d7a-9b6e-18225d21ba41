import React from 'react';
import type { ImageNativeProps } from './Image.types';
export default function ExpoImage({ source, placeholder, contentFit, contentPosition, placeholderContentFit, cachePolicy, onLoad, transition, onError, responsivePolicy, onLoadEnd, onDisplay, priority, blurRadius, recyclingKey, style, nativeViewRef, accessibilityLabel, tintColor, containerViewRef, ...props }: ImageNativeProps): React.JSX.Element;
//# sourceMappingURL=ExpoImage.web.d.ts.map
{"version": 3, "file": "Schemes.js", "sourceRoot": "", "sources": ["../src/Schemes.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,EAAE,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,MAAM,iBAAiB,GAAG,uCAAuC,CAAC;AAElE,eAAe;AACf,MAAM,UAAU,eAAe;IAC7B,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,IAAI,EAAE,CAAC;QACjE,oCAAoC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,UAAU,EAAE,CAAC;QAC9E,uDAAuD;QACvD,MAAM,eAAe,GAAG,sBAAsB,EAAE,CAAC;QACjD,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC;IAClC,CAAC;IACD,wCAAwC;IACxC,OAAO,KAAK,CAAC;AACf,CAAC;AAMD,SAAS,UAAU,CAAC,MAAuC;IACzD,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,CAAC,KAAU,EAAmB,EAAE;gBAC/C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC,CAAC,CAAC;YACF,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAS,QAAQ,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC7C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,qCAAqC;AACrC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC1C,oFAAoF;IACpF,GAAG,EAAE;QACH,KAAK;QACL,MAAM;QACN,oBAAoB;QACpB,mBAAmB;QACnB,0EAA0E;KAC3E;IACD,qBAAqB;IACrB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;CACzB,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,MAAM,UAAU,sBAAsB;IACpC,8EAA8E;IAC9E,8DAA8D;IAC9D,mEAAmE;IACnE,2CAA2C;IAC3C,MAAM,gBAAgB,GACnB,QAAQ,CAAC,MAAM,CAAM;QACpB,GAAG,EAAE,SAAS,CAAC,UAAU,EAAE,GAAG;QAC9B,OAAO,EAAE,SAAS,CAAC,UAAU,EAAE,OAAO;KACvC,CAAkB,IAAI,EAAE,CAAC;IAE5B,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,oBAAoB;IAC3B,6FAA6F;IAC7F,wFAAwF;IACxF,OAAO,CACL,QAAQ,CAAC,MAAM,CAAC;QACd,GAAG,EAAE,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,gBAAgB;QAChD,gEAAgE;QAChE,OAAO,EAAE,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO;KAChD,CAAC,IAAI,IAAI,CACX,CAAC;AACJ,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;AAC1D,CAAC;AAED,eAAe;AACf,MAAM,UAAU,aAAa,CAAC,OAAgD;IAC5E,IACE,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,WAAW;QACnE,CAAC,oBAAoB,EAAE,EACvB,CAAC;QACD,MAAM,IAAI,KAAK,CACb,oOAAoO,CACrO,CAAC;IACJ,CAAC;IAED,MAAM,eAAe,GAAG,sBAAsB,EAAE,CAAC;IACjD,MAAM,WAAW,GAAG,oBAAoB,EAAE,CAAC;IAE3C,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACjC,0HAA0H;YAC1H,OAAO,CAAC,IAAI,CACV,oVAAoV,iBAAiB,EAAE,CACxW,CAAC;QACJ,CAAC;aAAM,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAC3F,yIAAyI;YACzI,MAAM,IAAI,KAAK,CACb,6EAA6E,CAC9E,CAAC;QACJ,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,IAAI,SAAS,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,WAAW,EAAE,CAAC;QACxE,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,kFAAkF;YAClF,IAAI,mBAAmB,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClD,OAAO,OAAO,CAAC,MAAM,CAAC;YACxB,CAAC;YACD,4DAA4D;QAC9D,CAAC;QACD,yCAAyC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,OAAO,GAAG,CAAC,GAAG,eAAe,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAElE,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,CAAC;YACZ,0FAA0F;YAC1F,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC3D,oHAAoH;gBACpH,+DAA+D;gBAC/D,OAAO,CAAC,IAAI,CACV,gCACE,OAAO,CAAC,MACV,+FAA+F,OAAO;qBACnG,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC;qBAC9B,IAAI,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,kCAAkC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;IACD,kFAAkF;IAClF,mFAAmF;IACnF,kFAAkF;IAClF,mCAAmC;IAEnC,2CAA2C;IAC3C,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClE,qDAAqD;QACrD,2EAA2E;QAC3E,OAAO,CAAC,IAAI,CACV,2PAA2P,WAAW,kBAAkB,iBAAiB,EAAE,CAC5S,CAAC;QACF,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,qFAAqF;IACrF,yFAAyF;IACzF,yFAAyF;IACzF,kEAAkE;IAClE,+DAA+D;IAC/D,MAAM,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,GAAG,eAAe,CAAC;IAElD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,YAAY,GAAG,0NAA0N,iBAAiB,EAAE,CAAC;QACnQ,4GAA4G;QAC5G,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IACD,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC7C,OAAO,CAAC,IAAI,CACV,4EAA4E,MAAM,gBAAgB;YAChG,GAAG,YAAY;YACf,WAAW;SACZ;aACE,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,IAAI,CAAC,+DAA+D,CAC7E,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import Constants, { ExecutionEnvironment } from 'expo-constants';\nimport { Platform } from 'expo-modules-core';\n\nconst LINKING_GUIDE_URL = `https://docs.expo.dev/guides/linking/`;\n\n// @docsMissing\nexport function hasCustomScheme(): boolean {\n  if (Constants.executionEnvironment === ExecutionEnvironment.Bare) {\n    // <PERSON><PERSON> always uses a custom scheme.\n    return true;\n  } else if (Constants.executionEnvironment === ExecutionEnvironment.Standalone) {\n    // Standalone uses a custom scheme when one is defined.\n    const manifestSchemes = collectManifestSchemes();\n    return !!manifestSchemes.length;\n  }\n  // Store client uses the default scheme.\n  return false;\n}\n\ntype SchemeConfig = {\n  scheme?: string | string[];\n};\n\nfunction getSchemes(config: SchemeConfig | null | undefined): string[] {\n  if (config) {\n    if (Array.isArray(config.scheme)) {\n      const validate = (value: any): value is string => {\n        return typeof value === 'string';\n      };\n      return config.scheme.filter<string>(validate);\n    } else if (typeof config.scheme === 'string') {\n      return [config.scheme];\n    }\n  }\n  return [];\n}\n\n// Valid schemes for the Expo client.\nconst EXPO_CLIENT_SCHEMES = Platform.select({\n  // Results from `npx uri-scheme list --info-path ios/Exponent/Supporting/Info.plist`\n  ios: [\n    'exp',\n    'exps',\n    'fb1696089354000816',\n    'host.exp.exponent',\n    'com.googleusercontent.apps.603386649315-vp4revvrcgrcjme51ebuhbkbspl048l9',\n  ],\n  // Collected manually\n  android: ['exp', 'exps'],\n});\n\n/**\n * Collect a list of platform schemes from the manifest.\n *\n * This method is based on the `Scheme` modules from `@expo/config-plugins`\n * which are used for collecting the schemes before prebuilding a native app.\n *\n * - Android: `scheme` -> `android.scheme` -> `android.package`\n * - iOS: `scheme` -> `ios.scheme` -> `ios.bundleIdentifier`\n */\nexport function collectManifestSchemes(): string[] {\n  // ios.scheme, android.scheme, and scheme as an array are not yet added to the\n  // Expo config spec, but there's no harm in adding them early.\n  // They'll be added when we drop support for `expo build` or decide\n  // to have them only work with `eas build`.\n  const platformManifest =\n    (Platform.select<any>({\n      ios: Constants.expoConfig?.ios,\n      android: Constants.expoConfig?.android,\n    }) as SchemeConfig) ?? {};\n\n  return getSchemes(Constants.expoConfig).concat(getSchemes(platformManifest));\n}\n\nfunction getNativeAppIdScheme(): string | null {\n  // Add the native application identifier to the list of schemes for parity with `expo build`.\n  // The native app id has been added to builds for a long time to support Google Sign-In.\n  return (\n    Platform.select({\n      ios: Constants.expoConfig?.ios?.bundleIdentifier,\n      // TODO: This may change to android.applicationId in the future.\n      android: Constants.expoConfig?.android?.package,\n    }) ?? null\n  );\n}\n\n// @needsAudit\n/**\n * Ensure the user has linked the expo-constants manifest in bare workflow.\n */\nexport function hasConstantsManifest(): boolean {\n  return !!Object.keys(Constants.expoConfig ?? {}).length;\n}\n\n// @docsMissing\nexport function resolveScheme(options: { scheme?: string; isSilent?: boolean }): string {\n  if (\n    Constants.executionEnvironment !== ExecutionEnvironment.StoreClient &&\n    !hasConstantsManifest()\n  ) {\n    throw new Error(\n      `expo-linking needs access to the expo-constants manifest (app.json or app.config.js) to determine what URI scheme to use. Setup the manifest and rebuild: https://github.com/expo/expo/blob/main/packages/expo-constants/README.md`\n    );\n  }\n\n  const manifestSchemes = collectManifestSchemes();\n  const nativeAppId = getNativeAppIdScheme();\n\n  if (!manifestSchemes.length) {\n    if (__DEV__ && !options.isSilent) {\n      // Assert a config warning if no scheme is setup yet. `isSilent` is used for warnings, but we'll ignore it for exceptions.\n      console.warn(\n        `Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for production apps, if it's left blank, your app may crash. The scheme does not apply to development in the Expo client but you should add it as soon as you start working with Linking to avoid creating a broken build. Learn more: ${LINKING_GUIDE_URL}`\n      );\n    } else if (!__DEV__ || Constants.executionEnvironment !== ExecutionEnvironment.StoreClient) {\n      // Throw in production or when not in store client. Use the __DEV__ flag so users can test this functionality with `expo start --no-dev`,\n      throw new Error(\n        'Cannot make a deep link into a standalone app with no custom scheme defined'\n      );\n    }\n  }\n\n  // In the Expo client...\n  if (Constants.executionEnvironment === ExecutionEnvironment.StoreClient) {\n    if (options.scheme) {\n      // This enables users to use the fb or google redirects on iOS in the Expo client.\n      if (EXPO_CLIENT_SCHEMES?.includes(options.scheme)) {\n        return options.scheme;\n      }\n      // Silently ignore to make bare workflow development easier.\n    }\n    // Fallback to the default client scheme.\n    return 'exp';\n  }\n\n  const schemes = [...manifestSchemes, nativeAppId].filter(Boolean);\n\n  if (options.scheme) {\n    if (__DEV__) {\n      // Bare workflow development assertion about the provided scheme matching the Expo config.\n      if (!schemes.includes(options.scheme) && !options.isSilent) {\n        // TODO: Will this cause issues for things like Facebook or Google that use `reversed-client-id://` or `fb<FBID>:/`?\n        // Traditionally these APIs don't use the Linking API directly.\n        console.warn(\n          `The provided Linking scheme '${\n            options.scheme\n          }' does not appear in the list of possible URI schemes in your Expo config. Expected one of: ${schemes\n            .map((scheme) => `'${scheme}'`)\n            .join(', ')}`\n        );\n      }\n    }\n    // Return the user provided value.\n    return options.scheme;\n  }\n  // If no scheme is provided, we'll guess what the scheme is based on the manifest.\n  // This is to attempt to keep managed apps working across expo build and EAS build.\n  // EAS build ejects the app before building it so we can assume that the user will\n  // be using one of defined schemes.\n\n  // If the native app id is the only scheme,\n  if (!!nativeAppId && !manifestSchemes.length && !options.isSilent) {\n    // Assert a config warning if no scheme is setup yet.\n    // This warning only applies to managed workflow EAS apps, as bare workflow\n    console.warn(\n      `Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for bare or production apps. Manually providing a \\`scheme\\` property can circumvent this warning. Using native app identifier as the scheme '${nativeAppId}'. Learn more: ${LINKING_GUIDE_URL}`\n    );\n    return nativeAppId;\n  }\n  // When the native app id is defined, it'll be added to the list of schemes, for most\n  // users this will be unexpected behavior and cause all apps to warn when the Linking API\n  // is used without a predefined scheme. For now, if the native app id is defined, require\n  // at least one more scheme to be added before throwing a warning.\n  // i.e. `scheme: ['foo', 'bar']` (unimplemented functionality).\n  const [scheme, ...extraSchemes] = manifestSchemes;\n\n  if (!scheme) {\n    const errorMessage = `Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for bare or production apps. Manually providing a \\`scheme\\` property can circumvent this error. Learn more: ${LINKING_GUIDE_URL}`;\n    // Throw in production, use the __DEV__ flag so users can test this functionality with `expo start --no-dev`\n    throw new Error(errorMessage);\n  }\n  if (extraSchemes.length && !options.isSilent) {\n    console.warn(\n      `Linking found multiple possible URI schemes in your Expo config.\\nUsing '${scheme}'. Ignoring: ${[\n        ...extraSchemes,\n        nativeAppId,\n      ]\n        .filter(Boolean)\n        .join(', ')}.\\nPlease supply the preferred URI scheme to the Linking API.`\n    );\n  }\n  return scheme;\n}\n"]}
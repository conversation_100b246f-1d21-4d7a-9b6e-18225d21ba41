{"version": 3, "file": "Linking.server.js", "sourceRoot": "", "sources": ["../src/Linking.server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,MAAM,UAAU,gBAAgB,CAAC,IAAW,EAAE,OAAoB;IAChE,OAAO,EAAE,MAAM,KAAI,CAAC,EAAE,CAAC;AACzB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,OAAO;QACL,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,IAAI;KAClB,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,MAAc,EAAE,MAA2B;IAC1E,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AACzD,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,YAAY;IAChC,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa;IACjC,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,OAAO,CAAC,GAAW;IACvC,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,UAAU;IAC9B,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,MAAM;IACpB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,OAAO,IAAI,CAAC;AACd,CAAC;AAED,cAAc,iBAAiB,CAAC;AAEhC,MAAM,UAAU,sBAAsB;IACpC,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,oBAAoB;IAClC,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,eAAe;IAC7B,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport { ParsedURL, SendIntentExtras, URLListener } from './Linking.types';\n\nexport function addEventListener(type: 'url', handler: URLListener) {\n  return { remove() {} };\n}\n\nexport async function parseInitialURLAsync(): Promise<ParsedURL> {\n  return {\n    scheme: null,\n    hostname: null,\n    path: null,\n    queryParams: null,\n  };\n}\n\nexport async function sendIntent(action: string, extras?: SendIntentExtras[]): Promise<void> {\n  throw new UnavailabilityError('Linking', 'sendIntent');\n}\n\nexport async function openSettings(): Promise<void> {\n  throw new UnavailabilityError('Linking', 'openSettings');\n}\n\nexport async function getInitialURL(): Promise<string | null> {\n  return '';\n}\n\nexport function getLinkingURL() {\n  return '';\n}\n\nexport async function openURL(url: string): Promise<true> {\n  return true;\n}\n\nexport async function canOpenURL() {\n  return true;\n}\n\nexport function useURL(): string | null {\n  return null;\n}\n\nexport function useLinkingURL() {\n  return null;\n}\n\nexport * from './Linking.types';\n\nexport function collectManifestSchemes() {\n  return [];\n}\n\nexport function hasConstantsManifest() {\n  return false;\n}\n\nexport function hasCustomScheme() {\n  return false;\n}\n\nexport function resolveScheme() {\n  return '';\n}\n\nexport { parse, createURL } from './createURL';\n"]}
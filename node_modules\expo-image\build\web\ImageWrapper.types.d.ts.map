{"version": 3, "file": "ImageWrapper.types.d.ts", "sourceRoot": "", "sources": ["../../src/web/ImageWrapper.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,OAAO,CAAC;AAEtD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,0BAA0B,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAErF,MAAM,MAAM,YAAY,GACpB,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;IAAE,MAAM,EAAE,WAAW,GAAG,IAAI,CAAA;CAAE,KAAK,IAAI,CAAC,GACtD,SAAS,GACT,IAAI,CAAC;AACT,MAAM,MAAM,WAAW,GACnB,CAAC,CAAC,KAAK,EAAE,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,GAC1D,SAAS,GACT,IAAI,CAAC;AACT,MAAM,MAAM,oBAAoB,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;AACnE,MAAM,MAAM,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;AAC3D,MAAM,MAAM,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;AAE7D,MAAM,MAAM,kBAAkB,GAAG;IAC/B,MAAM,CAAC,EAAE,WAAW,EAAE,CAAC;IACvB,OAAO,CAAC,EAAE,YAAY,EAAE,CAAC;IACzB,eAAe,CAAC,EAAE,oBAAoB,EAAE,CAAC;IACzC,OAAO,CAAC,EAAE,YAAY,EAAE,CAAC;IACzB,SAAS,CAAC,EAAE,cAAc,EAAE,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,MAAM,CAAC,EAAE,WAAW,GAAG,YAAY,GAAG,IAAI,CAAC;IAC3C,MAAM,CAAC,EAAE,kBAAkB,CAAC;IAC5B,eAAe,CAAC,EAAE,0BAA0B,CAAC;IAC7C,8BAA8B,CAAC,EAAE,0BAA0B,CAAC;IAC5D,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,KAAK,EAAE,aAAa,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,oBAAoB,CAAC,EAAE,aAAa,CAAC;IACrC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,WAAW,CAAC,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;CACzC,CAAC"}
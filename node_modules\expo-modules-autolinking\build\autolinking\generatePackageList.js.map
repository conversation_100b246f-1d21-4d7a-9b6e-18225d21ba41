{"version": 3, "file": "generatePackageList.js", "sourceRoot": "", "sources": ["../../src/autolinking/generatePackageList.ts"], "names": [], "mappings": ";;;;;AASA,4DAaC;AAMD,oEAiBC;AA7CD,kDAA0B;AAE1B,mCAA8D;AAG9D;;;GAGG;AACI,KAAK,UAAU,wBAAwB,CAC5C,OAA2B,EAC3B,OAAwB;IAExB,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,IAAA,2CAAmC,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9E,MAAM,eAAe,CAAC,wBAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC7F,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,KAAK,CACX,eAAK,CAAC,GAAG,CAAC,0DAA0D,OAAO,CAAC,QAAQ,EAAE,CAAC,CACxF,CAAC;QACF,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,4BAA4B,CAChD,OAA2B,EAC3B,OAAuC;IAEvC,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,IAAA,2CAAmC,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9E,MAAM,eAAe,CAAC,4BAA4B,CAChD,OAAO,EACP,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,WAAW,CACpB,CAAC;IACJ,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,KAAK,CACX,eAAK,CAAC,GAAG,CAAC,8DAA8D,OAAO,CAAC,QAAQ,EAAE,CAAC,CAC5F,CAAC;QACF,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC", "sourcesContent": ["import chalk from 'chalk';\n\nimport { getLinkingImplementationForPlatform } from './utils';\nimport { GenerateModulesProviderOptions, GenerateOptions, ModuleDescriptor } from '../types';\n\n/**\n * Generates a source file listing all packages to link.\n * Right know it works only for Android platform.\n */\nexport async function generatePackageListAsync(\n  modules: ModuleDescriptor[],\n  options: GenerateOptions\n) {\n  try {\n    const platformLinking = getLinkingImplementationForPlatform(options.platform);\n    await platformLinking.generatePackageListAsync(modules, options.target, options.namespace);\n  } catch (e) {\n    console.error(\n      chalk.red(`Generating package list is not available for platform: ${options.platform}`)\n    );\n    throw e;\n  }\n}\n\n/**\n * Generates ExpoModulesProvider file listing all packages to link.\n * Right know it works only for Apple platforms.\n */\nexport async function generateModulesProviderAsync(\n  modules: ModuleDescriptor[],\n  options: GenerateModulesProviderOptions\n) {\n  try {\n    const platformLinking = getLinkingImplementationForPlatform(options.platform);\n    await platformLinking.generateModulesProviderAsync(\n      modules,\n      options.target,\n      options.entitlement\n    );\n  } catch (e) {\n    console.error(\n      chalk.red(`Generating modules provider is not available for platform: ${options.platform}`)\n    );\n    throw e;\n  }\n}\n"]}
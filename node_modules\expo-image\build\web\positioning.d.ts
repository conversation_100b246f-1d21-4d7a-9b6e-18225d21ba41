import { ImageContentPositionObject } from '../Image.types';
export declare function ensureValueIsWebUnits(value: string | number): string;
export declare const absoluteFilledPosition: {
    readonly width: "100%";
    readonly height: "100%";
    readonly position: "absolute";
    readonly left: 0;
    readonly top: 0;
};
export declare function getObjectPositionFromContentPositionObject(contentPosition?: ImageContentPositionObject): string;
//# sourceMappingURL=positioning.d.ts.map
{"version": 3, "file": "verifySearchResults.js", "sourceRoot": "", "sources": ["../../src/autolinking/verifySearchResults.ts"], "names": [], "mappings": ";;;;;AAQA,kDAwBC;AAhCD,kDAA0B;AAC1B,gDAAwB;AAIxB;;GAEG;AACH,SAAgB,mBAAmB,CAAC,aAA4B,EAAE,OAAsB;IACtF,MAAM,YAAY,GAAqC,CAAC,GAAG,EAAE,EAAE,CAC7D,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;QAE3C,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,kCAAkC,eAAK,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,MAAM,eAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,eAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAE7F,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,MAAM,eAAK,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,eAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9F,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IACD,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;QAChB,OAAO,CAAC,IAAI,CACV,kIAAkI,CACnI,CAAC;IACJ,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import chalk from 'chalk';\nimport path from 'path';\n\nimport { PackageRevision, SearchOptions, SearchResults } from '../types';\n\n/**\n * Verifies the search results by checking whether there are no duplicates.\n */\nexport function verifySearchResults(searchResults: SearchResults, options: SearchOptions): number {\n  const relativePath: (pkg: PackageRevision) => string = (pkg) =>\n    path.relative(options.projectRoot, pkg.path);\n  let counter = 0;\n\n  for (const moduleName in searchResults) {\n    const revision = searchResults[moduleName];\n\n    if (revision.duplicates?.length) {\n      console.warn(`⚠️  Found multiple versions of ${chalk.green(moduleName)}`);\n      console.log(` - ${chalk.magenta(relativePath(revision))} (${chalk.cyan(revision.version)})`);\n\n      for (const duplicate of revision.duplicates) {\n        console.log(` - ${chalk.gray(relativePath(duplicate))} (${chalk.gray(duplicate.version)})`);\n      }\n      counter++;\n    }\n  }\n  if (counter > 0) {\n    console.warn(\n      '⚠️  Multiple versions of the same module may introduce some side effects or compatibility issues. Remove the duplicate versions.'\n    );\n  }\n  return counter;\n}\n"]}
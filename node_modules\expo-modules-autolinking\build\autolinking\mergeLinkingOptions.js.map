{"version": 3, "file": "mergeLinkingOptions.js", "sourceRoot": "", "sources": ["../../src/autolinking/mergeLinkingOptions.ts"], "names": [], "mappings": ";;;;;AASA,wEAMC;AAKD,sEAMC;AAQD,4DAyBC;AAMD,0DAOC;AAxED,sDAA6B;AAC7B,4CAAoB;AACpB,gDAAwB;AAIxB;;GAEG;AACI,KAAK,UAAU,8BAA8B,CAAC,WAAmB;IACtE,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAM,EAAC,cAAc,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,WAAW,GAAG,CAAC,CAAC;IAChF,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAAC,WAAmB;IAC/D,MAAM,MAAM,GAAG,iBAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;IACjE,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,8CAA8C,WAAW,GAAG,CAAC,CAAC;IAChF,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,wBAAwB,CAC5C,eAA4B;IAE5B,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,8BAA8B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC;IAC/F,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,WAAyC,CAAC;IAChF,MAAM,eAAe,GAAG,kBAAkB,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAClF,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAChC,EAAE,EACF,WAAW,EACX,eAAe,EACf,eAAe,CACD,CAAC;IAEjB,qFAAqF;IACrF,YAAY,CAAC,WAAW,GAAG,MAAM,uBAAuB,CACtD,YAAY,CAAC,WAAW,EACxB,eAAe,CAAC,WAAW,CAC5B,CAAC;IAEF,YAAY,CAAC,gBAAgB,GAAG,MAAM,4BAA4B,CAChE,YAAY,CAAC,gBAAgB,EAC7B,eAAe,CAAC,WAAW,CAC5B,CAAC;IAEF,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,uBAAuB,CAC3C,WAA4B,EAC5B,GAAW;IAEX,OAAO,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;QAC1C,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAChE,CAAC,CAAC,MAAM,qBAAqB,CAAC,GAAG,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,GAAW;IAC9C,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,GAAG,GAAG,GAAG,CAAC;IACd,IAAI,WAA+B,CAAC;IAEpC,OAAO,CAAC,WAAW,GAAG,MAAM,IAAA,iBAAM,EAAC,cAAc,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;QAClE,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;QAEzD,gFAAgF;QAChF,IAAI,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9B,MAAM;QACR,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;GAUG;AACH,KAAK,UAAU,4BAA4B,CACzC,gBAA2C,EAC3C,GAAW;IAEX,MAAM,eAAe,GAAG,MAAM,IAAA,iBAAM,EAAC,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9D,MAAM,WAAW,GAAG,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACrF,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,IAAI,SAAS,CAAC,CAAC;IAC9E,OAAO,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3D,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,QAA2B,EAC3B,OAAoC;IAEpC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,OAAO,OAAO,EAAE,KAAK,IAAI,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC;IAC9C,CAAC;IACD,OAAO,OAAO,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACnC,CAAC", "sourcesContent": ["import findUp from 'find-up';\nimport fs from 'fs';\nimport path from 'path';\n\nimport type { PlatformAutolinkingOptions, SearchOptions, SupportedPlatform } from '../types';\n\n/**\n * Find the path to the `package.json` of the closest project in the given project root.\n */\nexport async function getProjectPackageJsonPathAsync(projectRoot: string): Promise<string> {\n  const result = await findUp('package.json', { cwd: projectRoot });\n  if (!result) {\n    throw new Error(`Couldn't find \"package.json\" up from path \"${projectRoot}\"`);\n  }\n  return result;\n}\n\n/**\n * Synchronous version of {@link getProjectPackageJsonPathAsync}.\n */\nexport function getProjectPackageJsonPathSync(projectRoot: string): string {\n  const result = findUp.sync('package.json', { cwd: projectRoot });\n  if (!result) {\n    throw new Error(`Couldn't find \"package.json\" up from path \"${projectRoot}\"`);\n  }\n  return result;\n}\n\n/**\n * Merges autolinking options from different sources (the later the higher priority)\n * - options defined in package.json's `expo.autolinking` field\n * - platform-specific options from the above (e.g. `expo.autolinking.apple`)\n * - options provided to the CLI command\n */\nexport async function mergeLinkingOptionsAsync<OptionsType extends SearchOptions>(\n  providedOptions: OptionsType\n): Promise<OptionsType> {\n  const packageJson = require(await getProjectPackageJsonPathAsync(providedOptions.projectRoot));\n  const baseOptions = packageJson.expo?.autolinking as PlatformAutolinkingOptions;\n  const platformOptions = getPlatformOptions(providedOptions.platform, baseOptions);\n  const finalOptions = Object.assign(\n    {},\n    baseOptions,\n    platformOptions,\n    providedOptions\n  ) as OptionsType;\n\n  // Makes provided paths absolute or falls back to default paths if none was provided.\n  finalOptions.searchPaths = await resolveSearchPathsAsync(\n    finalOptions.searchPaths,\n    providedOptions.projectRoot\n  );\n\n  finalOptions.nativeModulesDir = await resolveNativeModulesDirAsync(\n    finalOptions.nativeModulesDir,\n    providedOptions.projectRoot\n  );\n\n  return finalOptions;\n}\n\n/**\n * Resolves autolinking search paths. If none is provided, it accumulates all node_modules when\n * going up through the path components. This makes workspaces work out-of-the-box without any configs.\n */\nexport async function resolveSearchPathsAsync(\n  searchPaths: string[] | null,\n  cwd: string\n): Promise<string[]> {\n  return searchPaths && searchPaths.length > 0\n    ? searchPaths.map((searchPath) => path.resolve(cwd, searchPath))\n    : await findDefaultPathsAsync(cwd);\n}\n\n/**\n * Looks up for workspace's `node_modules` paths.\n */\nasync function findDefaultPathsAsync(cwd: string): Promise<string[]> {\n  const paths = [];\n  let dir = cwd;\n  let pkgJsonPath: string | undefined;\n\n  while ((pkgJsonPath = await findUp('package.json', { cwd: dir }))) {\n    dir = path.dirname(path.dirname(pkgJsonPath));\n    paths.push(path.join(pkgJsonPath, '..', 'node_modules'));\n\n    // This stops the infinite loop when the package.json is placed at the root dir.\n    if (path.dirname(dir) === dir) {\n      break;\n    }\n  }\n  return paths;\n}\n\n/**\n * Finds the real path to custom native modules directory.\n * - When {@link cwd} is inside the project directory, the path is searched relatively\n * to the project root (directory with the `package.json` file).\n * - When {@link cwd} is outside project directory (no `package.json` found), it is relative to\n * the current working directory (the {@link cwd} param).\n *\n * @param nativeModulesDir path to custom native modules directory. Defaults to `\"./modules\"` if null.\n * @param cwd current working directory\n * @returns resolved native modules directory or `null` if it is not found or doesn't exist.\n */\nasync function resolveNativeModulesDirAsync(\n  nativeModulesDir: string | null | undefined,\n  cwd: string\n): Promise<string | null> {\n  const packageJsonPath = await findUp('package.json', { cwd });\n  const projectRoot = packageJsonPath != null ? path.join(packageJsonPath, '..') : cwd;\n  const resolvedPath = path.resolve(projectRoot, nativeModulesDir || 'modules');\n  return fs.existsSync(resolvedPath) ? resolvedPath : null;\n}\n\n/**\n * Gets the platform-specific autolinking options from the base options.\n */\nfunction getPlatformOptions(\n  platform: SupportedPlatform,\n  options?: PlatformAutolinkingOptions\n): PlatformAutolinkingOptions {\n  if (platform === 'apple') {\n    return options?.apple ?? options?.ios ?? {};\n  }\n  return options?.[platform] ?? {};\n}\n"]}
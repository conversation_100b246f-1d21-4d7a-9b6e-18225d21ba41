{"version": 3, "file": "resolveModules.js", "sourceRoot": "", "sources": ["../../src/autolinking/resolveModules.ts"], "names": [], "mappings": ";;AAMA,kDA0BC;AAKD,gFAQC;AA7CD,mCAA8D;AAG9D;;GAEG;AACI,KAAK,UAAU,mBAAmB,CACvC,aAA4B,EAC5B,OAAuB;IAEvB,MAAM,eAAe,GAAG,IAAA,2CAAmC,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAE9E,OAAO,CACL,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,EAAE;QAClE,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,kBAAkB,CAC7D,WAAW,EACX,QAAQ,EACR,OAAO,CACR,CAAC;QACF,OAAO,cAAc;YACnB,CAAC,CAAC;gBACE,WAAW;gBACX,cAAc,EAAE,QAAQ,CAAC,OAAO;gBAChC,GAAG,cAAc;aAClB;YACH,CAAC,CAAC,IAAI,CAAC;IACX,CAAC,CAAC,CACH,CACF;SACE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAChE,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kCAAkC,CACtD,OAAuB;IAEvB,MAAM,eAAe,GAAG,IAAA,2CAAmC,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9E,MAAM,iBAAiB,GAAG,MAAM,eAAe,CAAC,kCAAkC,CAChF,OAAO,CAAC,WAAW,CACpB,CAAC;IACF,OAAO,iBAAiB,IAAI,EAAE,CAAC;AACjC,CAAC", "sourcesContent": ["import { getLinkingImplementationForPlatform } from './utils';\nimport type { ExtraDependencies, ModuleDescriptor, ResolveOptions, SearchResults } from '../types';\n\n/**\n * Resolves search results to a list of platform-specific configuration.\n */\nexport async function resolveModulesAsync(\n  searchResults: SearchResults,\n  options: ResolveOptions\n): Promise<ModuleDescriptor[]> {\n  const platformLinking = getLinkingImplementationForPlatform(options.platform);\n\n  return (\n    await Promise.all(\n      Object.entries(searchResults).map(async ([packageName, revision]) => {\n        const resolvedModule = await platformLinking.resolveModuleAsync(\n          packageName,\n          revision,\n          options\n        );\n        return resolvedModule\n          ? {\n              packageName,\n              packageVersion: revision.version,\n              ...resolvedModule,\n            }\n          : null;\n      })\n    )\n  )\n    .filter(Boolean)\n    .sort((a, b) => a.packageName.localeCompare(b.packageName));\n}\n\n/**\n * Resolves the extra build dependencies for the project, such as additional Maven repositories or CocoaPods pods.\n */\nexport async function resolveExtraBuildDependenciesAsync(\n  options: ResolveOptions\n): Promise<ExtraDependencies> {\n  const platformLinking = getLinkingImplementationForPlatform(options.platform);\n  const extraDependencies = await platformLinking.resolveExtraBuildDependenciesAsync(\n    options.projectRoot\n  );\n  return extraDependencies ?? [];\n}\n"]}